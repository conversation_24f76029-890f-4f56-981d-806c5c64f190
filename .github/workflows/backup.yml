# This is a basic workflow to help you get started with Actions

name: CI

# Controls when the action will run.
# on:
#   # Triggers the workflow on push or pull request events but only for the main branch
#   push:
#     branches: [main]
#   pull_request:
#     branches: [main]
on:
  # schedule:
  #   - cron: "0 7 * * *" # 每天7点执行 , 去掉此处注释开启触发
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4.5.0
        with:
          python-version: "3.10"
      - name: install dependency
        run: pip3 install requests
      - name: run notion_backup.py
        env:
          NOTION_TOKEN: ${{ secrets.NOTION_TOKEN }} # 在后台配置好token,或者用NOTION_EMAIL和NOTION_PASSWORD也可以
          WXPUSHER_TOKEN: ${{ secrets.WXPUSHER_TOKEN }} # wxpusher 的 appToken
          WXPUSHER_UIDS: ${{ secrets.WXPUSHER_UIDS }} # wxpusher 用户的 UID
        run: |
          echo '开始执行'
          python -u notion_backup.py
          echo '执行完成'
